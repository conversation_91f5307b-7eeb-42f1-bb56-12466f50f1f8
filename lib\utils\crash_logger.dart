import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 全局异常捕获和日志管理器
class CrashLogger {
  static CrashLogger? _instance;
  static CrashLogger get instance => _instance ??= CrashLogger._();

  CrashLogger._();

  /// 日志文件名
  static const String _logFileName = 'crash_logs.txt';

  /// 最大日志文件大小 (5MB)
  static const int _maxLogFileSize = 5 * 1024 * 1024;

  /// 最大保留日志条数
  static const int _maxLogEntries = 1000;

  /// 初始化全局异常捕获
  static void init() {
    logger.i('Initializing CrashLogger...');

    // 捕获Flutter框架异常
    FlutterError.onError = (FlutterErrorDetails details) {
      logger.e('Flutter Error caught: ${details.exception}');
      FlutterError.presentError(details);
      instance._logError(
        'Flutter Error',
        details.exception.toString(),
        details.stack?.toString(),
        details.context?.toString(),
      );
    };

    // 捕获Dart异常
    PlatformDispatcher.instance.onError = (error, stack) {
      logger.e('Dart Error caught: $error');
      instance._logError(
        'Dart Error',
        error.toString(),
        stack.toString(),
        null,
      );
      return true;
    };

    logger.i('CrashLogger initialized successfully');

    // 添加一个初始化日志
    Future.delayed(const Duration(milliseconds: 100), () {
      instance._logError(
        'System',
        'CrashLogger initialized at ${DateTime.now()}',
        null,
        'Application startup',
      );
    });
  }

  /// 记录异常信息
  void _logError(
      String type, String error, String? stackTrace, String? context) {
    final logEntry = CrashLogEntry(
      timestamp: DateTime.now(),
      type: type,
      error: error,
      stackTrace: stackTrace,
      context: context,
    );

    _saveLogEntry(logEntry);

    // 同时输出到控制台
    logger.e('$type: $error');
    if (stackTrace != null) {
      logger.e('Stack trace: $stackTrace');
    }
  }

  /// 手动记录异常
  static void logException(String type, dynamic exception,
      [StackTrace? stackTrace]) {
    instance._logError(
      type,
      exception.toString(),
      stackTrace?.toString(),
      null,
    );
  }

  /// 添加测试日志
  static void addTestLog() {
    logger.i('Adding test log...');
    instance._logError(
      'Test Error',
      '这是一个测试异常，用于验证异常捕获功能是否正常工作',
      StackTrace.current.toString(),
      'Test context from settings page',
    );
  }

  /// 获取日志文件路径（用于调试）
  static Future<String> getLogFilePath() async {
    if (kIsWeb) {
      return 'SharedPreferences:crash_logs';
    }
    final file = await instance._getLogFile();
    return file.path;
  }

  /// 检查日志文件是否存在（用于调试）
  static Future<bool> logFileExists() async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey('crash_logs');
    }
    final file = await instance._getLogFile();
    return await file.exists();
  }

  /// 创建可分享的日志文件
  static Future<File?> createShareableLogFile() async {
    try {
      final logs = await instance.getAllLogs();
      if (logs.isEmpty) {
        logger.w('No logs to share');
        return null;
      }

      // Web 平台不支持文件系统，返回 null
      if (kIsWeb) {
        logger.w('File sharing not supported on web platform');
        return null;
      }

      // 创建一个临时的可读日志文件
      final directory = await getApplicationDocumentsDirectory();
      final shareFile = File('${directory.path}/crash_logs_export.txt');

      final buffer = StringBuffer();
      buffer.writeln('=== 异常日志报告 ===');
      buffer.writeln('导出时间: ${DateTime.now().toString()}');
      buffer.writeln('日志总数: ${logs.length}');
      buffer.writeln('应用版本: 1.1.03'); // 可以从package_info获取
      buffer.writeln('');

      for (int i = 0; i < logs.length; i++) {
        buffer.writeln('=== 日志 ${i + 1} ===');
        buffer.writeln(logs[i].fullContent);
        buffer.writeln('');
      }

      await shareFile.writeAsString(buffer.toString());
      logger.i('Created shareable log file: ${shareFile.path}');
      return shareFile;
    } catch (e) {
      logger.e('Failed to create shareable log file: $e');
      return null;
    }
  }

  /// 保存日志条目到文件或SharedPreferences
  Future<void> _saveLogEntry(CrashLogEntry entry) async {
    try {
      if (kIsWeb) {
        // Web 平台使用 SharedPreferences
        await _saveLogEntryToPrefs(entry);
      } else {
        // 移动端使用文件系统
        final file = await _getLogFile();
        final logLine = '${jsonEncode(entry.toJson())}\n';
        await file.writeAsString(logLine, mode: FileMode.append);

        logger.i('Saved crash log entry to file: ${file.path}');

        // 检查文件大小，如果超过限制则清理
        await _checkAndCleanLogs();
      }
    } catch (e) {
      logger.e('Failed to save log entry: $e');
    }
  }

  /// 保存日志条目到 SharedPreferences (Web 平台)
  Future<void> _saveLogEntryToPrefs(CrashLogEntry entry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logs = await _getLogsFromPrefs();

      logs.add(entry);

      // 限制日志数量
      if (logs.length > _maxLogEntries) {
        logs.removeRange(0, logs.length - _maxLogEntries);
      }

      final logsJson = logs.map((log) => jsonEncode(log.toJson())).toList();
      await prefs.setStringList('crash_logs', logsJson);

      logger.i('Saved crash log entry to SharedPreferences');
    } catch (e) {
      logger.e('Failed to save log entry to SharedPreferences: $e');
    }
  }

  /// 获取日志文件 (仅移动端)
  Future<File> _getLogFile() async {
    if (kIsWeb) {
      throw UnsupportedError('File system not supported on web platform');
    }
    final directory = await getApplicationDocumentsDirectory();
    return File('${directory.path}/$_logFileName');
  }

  /// 从 SharedPreferences 获取日志 (Web 平台)
  Future<List<CrashLogEntry>> _getLogsFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = prefs.getStringList('crash_logs') ?? [];

      final logs = <CrashLogEntry>[];
      for (final logJson in logsJson) {
        try {
          final json = jsonDecode(logJson);
          logs.add(CrashLogEntry.fromJson(json));
        } catch (e) {
          logger.w('Failed to parse log from SharedPreferences: $logJson');
        }
      }

      return logs;
    } catch (e) {
      logger.e('Failed to get logs from SharedPreferences: $e');
      return [];
    }
  }

  /// 检查并清理日志文件
  Future<void> _checkAndCleanLogs() async {
    try {
      if (kIsWeb) {
        // Web 平台的清理在 _saveLogEntryToPrefs 中已经处理
        return;
      }

      final file = await _getLogFile();
      if (!await file.exists()) return;

      final stat = await file.stat();
      if (stat.size > _maxLogFileSize) {
        await _cleanOldLogs();
      }
    } catch (e) {
      logger.e('Failed to check log file size: $e');
    }
  }

  /// 清理旧日志，保留最新的条目
  Future<void> _cleanOldLogs() async {
    try {
      if (kIsWeb) {
        // Web 平台的清理在 _saveLogEntryToPrefs 中已经处理
        return;
      }

      final logs = await getAllLogs();
      if (logs.length <= _maxLogEntries) return;

      // 保留最新的日志条目
      final recentLogs = logs.take(_maxLogEntries).toList();

      final file = await _getLogFile();
      final buffer = StringBuffer();
      for (final log in recentLogs) {
        buffer.writeln(jsonEncode(log.toJson()));
      }

      await file.writeAsString(buffer.toString());
      logger.i('Cleaned old logs, kept ${recentLogs.length} entries');
    } catch (e) {
      logger.e('Failed to clean old logs: $e');
    }
  }

  /// 获取所有日志
  Future<List<CrashLogEntry>> getAllLogs() async {
    try {
      if (kIsWeb) {
        // Web 平台从 SharedPreferences 获取
        final logs = await _getLogsFromPrefs();
        // 按时间倒序排列（最新的在前面）
        logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        return logs;
      } else {
        // 移动端从文件获取
        final file = await _getLogFile();
        if (!await file.exists()) return [];

        final content = await file.readAsString();
        final lines =
            content.split('\n').where((line) => line.trim().isNotEmpty);

        final logs = <CrashLogEntry>[];
        for (final line in lines) {
          try {
            final json = jsonDecode(line);
            logs.add(CrashLogEntry.fromJson(json));
          } catch (e) {
            logger.w('Failed to parse log line: $line');
          }
        }

        // 按时间倒序排列（最新的在前面）
        logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        return logs;
      }
    } catch (e) {
      logger.e('Failed to read logs: $e');
      return [];
    }
  }

  /// 清空所有日志
  Future<void> clearAllLogs() async {
    try {
      if (kIsWeb) {
        // Web 平台清空 SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('crash_logs');
      } else {
        // 移动端删除文件
        final file = await _getLogFile();
        if (await file.exists()) {
          await file.delete();
        }
      }
      logger.i('All crash logs cleared');
    } catch (e) {
      logger.e('Failed to clear logs: $e');
    }
  }

  /// 获取日志文件大小（格式化字符串）
  Future<String> getLogFileSize() async {
    try {
      if (kIsWeb) {
        // Web 平台计算 SharedPreferences 中日志的大小
        final logs = await _getLogsFromPrefs();
        final totalSize = logs.fold<int>(0, (sum, log) {
          return sum + jsonEncode(log.toJson()).length;
        });
        return _formatFileSize(totalSize);
      } else {
        // 移动端获取文件大小
        final file = await _getLogFile();
        if (!await file.exists()) return '0 B';

        final stat = await file.stat();
        return _formatFileSize(stat.size);
      }
    } catch (e) {
      return '未知';
    }
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// 异常日志条目
class CrashLogEntry {
  final DateTime timestamp;
  final String type;
  final String error;
  final String? stackTrace;
  final String? context;

  CrashLogEntry({
    required this.timestamp,
    required this.type,
    required this.error,
    this.stackTrace,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'error': error,
      'stackTrace': stackTrace,
      'context': context,
    };
  }

  factory CrashLogEntry.fromJson(Map<String, dynamic> json) {
    return CrashLogEntry(
      timestamp: DateTime.parse(json['timestamp']),
      type: json['type'],
      error: json['error'],
      stackTrace: json['stackTrace'],
      context: json['context'],
    );
  }

  /// 获取格式化的时间字符串
  String get formattedTime {
    return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')} '
        '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
  }

  /// 获取简短的错误描述
  String get shortError {
    if (error.length <= 100) return error;
    return '${error.substring(0, 100)}...';
  }

  /// 获取完整的日志内容（用于复制）
  String get fullContent {
    final buffer = StringBuffer();
    buffer.writeln('时间: $formattedTime');
    buffer.writeln('类型: $type');
    buffer.writeln('错误: $error');

    if (context != null && context!.isNotEmpty) {
      buffer.writeln('上下文: $context');
    }

    if (stackTrace != null && stackTrace!.isNotEmpty) {
      buffer.writeln('堆栈跟踪:');
      buffer.writeln(stackTrace);
    }

    return buffer.toString();
  }
}
